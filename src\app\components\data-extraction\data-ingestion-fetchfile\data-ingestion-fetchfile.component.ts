import { Component, ElementRef, HostListener, OnInit, ViewChild } from "@angular/core";
import { interval, Subscription } from "rxjs";
import {
  DocumentTypeOption,
  FileConfig,
  FileDetails,
  IngestionFormData,
  PortfolioLogoModel,
  SelectedFile,
} from "../data-ingestion/data-ingestion.model";
import { DataIngestionUtility } from "../data-ingestion/data-Ingestion-utility";
import { ToastContainerDirective, ToastrService } from "ngx-toastr";
import {
  CommonConstants,
  DataIngestionConstants,
  DataIngestionModuleConstants,
} from "src/app/common/constants";
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  ValidatorFn,
  Validators,
} from "@angular/forms";
import { filePdfIcon, SVGIcon } from "@progress/kendo-svg-icons";
import { DataIngestionService } from "src/app/services/data-ingestion.service";
import { PopoverContainerDirective } from "@progress/kendo-angular-tooltip";
import { PortfolioCompanyService } from "src/app/services/portfolioCompany.service";
import { ActivatedRoute, Router } from "@angular/router";
import { PdfModel, PdfSourceModel } from "./pdf-preview/pdf.model";
import { ExtractionSharedService } from "src/app/services/extraction-shared.service";
import {
  ExtractionDefaultPeriodTypes,
  ExtractionUiConstants,
  StateDescriptions,
  StatusConstants,
} from "../extraction-constants";
import {CommonFileModel,
  CreateJobs,
  DocumentSummaryDto,
  FileConfigurationDetails,
  IngestConfigurationModel,
  IngestionResponseModel,
  IngestionStartModel,
  ProcessDetailsDto,
  RepositoryNode,
  S3UrlExtractedModel,
  StateAndStatus,
  UploadResponse,
} from "../extraction.model";
import { ExtractionUtility } from "../extraction-utility";
import {
  TablePageDetails,
  TableSuggestionResponse,
} from "../table-suggestions.model";
import { TableSuggestionsUtility } from "../table-suggestions.utility";
import { KpiConfig } from "../models/kpi-config.model";
import { environment } from "src/environments/environment";
import { SafeResourceUrl } from "@angular/platform-browser";
import { OidcAuthService } from "src/app/services/oidc-auth.service";
import { ExtractionIngestionService } from "src/app/services/extraction-ingestion.service";
import { RepositoryConfigService } from "src/app/services/repository.config.service";
import { FeaturesEnum } from "src/app/services/permission.service";
@Component({
  selector: "app-data-ingestion-fetch-file",
  templateUrl: "./data-ingestion-fetchfile.component.html",
  styleUrls: ["./data-ingestion-fetchfile.component.scss"],
})
export class DataIngestionFetchFileComponent implements OnInit {
  previewUrl: string = environment.ingestion_url + '/#/preview';
  trustedPreviewUrl: SafeResourceUrl
  modules: PdfModel[] = DataIngestionModuleConstants;
  pdfSource: PdfSourceModel = null;
  showPreview: boolean = false;
  pdfIcon = filePdfIcon;
  selectedFilesList: SelectedFile[] = [];
  selectedFiles: FileDetails[] = [];
  backupUploadedFiles: SelectedFile[] = [];
  backupUploadedFileNames: FileDetails[] = [];
  ingestionFormData: IngestionFormData | null = null;
  private readonly subscription: Subscription;
  companyName: string = null;
  companyInitials: string = null;
  reportingPeriod = null;
  isLogo: boolean = false;
  @ViewChild("fetchFileInput") fileInput!: ElementRef;
  fileConfig: FileConfig = {
    maxSize: 500 * 1024 * 1024, // 500MB
    maxFiles: 1,
    allowedTypes: [],
  };
  fileErrors: string[] = [];
  @ViewChild(ToastContainerDirective, {})
  toastContainer: ToastContainerDirective;
  gridForm: FormGroup;
  showDialog: boolean = false;
  selectedFileErrors: string[] = [];
  dialogStyle: any = {};
  documentTypes: DocumentTypeOption[] = [];
  sourceDocumentTypes: DocumentTypeOption[] = [];
  @ViewChild("container") private readonly container: PopoverContainerDirective;
  companyEncryptedID: string = null;
  popoverStates: { [key: number]: boolean } = {};
  companyLogo: string = null;
  s3UploadResponse: UploadResponse[] = [];
  s3UrlExtractedModel: S3UrlExtractedModel[] = [];
  fileFormatModel: CommonFileModel[] = [];
  docIngest: IngestConfigurationModel;
  isLoader: boolean = false;
  spread_fs_start_Model: IngestionStartModel;
  add_update_job: CreateJobs;
  stateAndStatus: StateAndStatus[] = [];
  tablePageNumbers: TablePageDetails[] = [];
  classifierData: TableSuggestionResponse;
  kpiConfig: KpiConfig[] = [];
  private statusCheckSubscription: Subscription;
  private readonly POLLING_INTERVAL = 15000; // 30 seconds in milliseconds
  private readonly COMPLETED_STATUS = "COMPLETED"; // Replace with your actual completed status
  private readonly FAILED_STATUS = "FAILED"; 
  processId: string = null;
  processDetails: ProcessDetailsDto;
  documents: DocumentSummaryDto[] = [];
  config:any;
  pageConfigModel: any[] = [];
  selectionForm: FormGroup;
  showSelectionPopup: boolean = false;
  repositoryData: RepositoryNode[] = [];
  documentTypeMap = new Map<string, string>();
  availableYears: any[] = [];
  availableMonths: any[] = [];
  availableQuarters: any[] = [];
  selectedDocumentStructure: any;
  documentStructureMap = new Map<string, any>();
  isPeriodSelectionEnabled= true;
  periodTypes: { id: string; name: string }[] = [];
  constructor(
    private readonly extractionIngestionService: ExtractionIngestionService,
    private readonly toasterService: ToastrService,
    private readonly fb: FormBuilder,
    private readonly dataIngestion: DataIngestionService,
    private readonly portfolioCompanyService: PortfolioCompanyService,
    private readonly router: Router,
    private readonly extractionSharedService: ExtractionSharedService,
    private _avRoute: ActivatedRoute,
    private readonly oidcService: OidcAuthService,
    private readonly repositoryConfigService: RepositoryConfigService, 
  ) {
    if (this._avRoute.snapshot.params["id"]) {
      this.processId = this._avRoute.snapshot.params["id"];
    }
    this.initForm();
    this.initSelectionForm();
  }
  getProcessDetails() {
    return new Promise<void>((resolve) => {
      this.extractionSharedService
        .get(ExtractionUiConstants.GET_PROCESS_DeTAILS(this.processId))
        .subscribe({
          next: (result: ProcessDetailsDto) => {
            this.processDetails = result;
            if (result.documents != null && result.documents.length > 0) {
              this.documents = result.documents;
            }
            this.companyEncryptedID = result.encryptedPortfolioCompanyId;
            this.companyName = result.companyName;             
            resolve();
          },
          error: (error) => {
            console.error("Error loading process details:", error);
            this.processDetails = { parentJobId: null } as ProcessDetailsDto; // Initialize with default values
            resolve(); // Resolve even on error to ensure dependent methods are called
          },
        });
    });
  }

  convertDocumentsToFiles(documents: DocumentSummaryDto[]): void {
    const processedFiles: SelectedFile[] = [];
    const fileDetails: FileDetails[] = [];

    documents.forEach((doc) => {
      const selectedFile: SelectedFile = {
        id: doc.id,
        name: doc.name,
        status:
          doc.errors.length > 0
            ? DataIngestionConstants.INVALID
            : DataIngestionConstants.VALID,
        errors: doc.errors,
        documentType: doc.documentType || null,
        file: new File([], doc.name, { type: doc.type }),
        type: doc.type,
        size: 0,
      };
      const fileDetail: FileDetails = {
        id: selectedFile.id,
        name: selectedFile.name,
      };
      processedFiles.push(selectedFile);
      fileDetails.push(fileDetail);
    });

    this.selectedFilesList = processedFiles;
    this.selectedFiles = fileDetails;
    this.updateUIState();
  }
  /**
   * Loads available document extraction types from the API
   */
  loadExtractionTypes(): void {
    this.dataIngestion.getDataExtractionTypes(FeaturesEnum.PortfolioCompany).subscribe({
      next: (data: DocumentTypeOption[]) => {
        this.documentTypes = data;
        this.sourceDocumentTypes = data;
      },
      error: (error) => {
        this.documentTypes = [];
      },
    });
  }

  /**
   * Initializes the toaster container for notifications
   */
  initializeToaster(): void {
    this.toasterService.overlayContainer = this.toastContainer;
  }
  getStateAndStatus() {
    this.extractionSharedService
      .get(ExtractionUiConstants.StateAndStatus)
      .subscribe({
        next: (result: StateAndStatus[]) => {
          this.stateAndStatus = result;
        },
      });
  }
  ngOnInit() {
    this.config = this.oidcService.getEnvironmentConfig();   
    this.getStateAndStatus();
    this.loadExtractionTypes();
    this.initializeToaster();
    if (this.processId) {
      this.loadModules();
      this.getProcessDetails().then(() => {
        this.convertDocumentsToFiles(this.documents);
        this.getPortfolioCompanies();      
        this.loadDocumentTypes();
        if (!this.isLogo) this.initializeCompanyInitials();
        const hasErrors = this.documents.some(
          (doc) =>
            doc &&
            doc.errors &&
            Array.isArray(doc.errors) &&
            doc.errors.length > 0
        );
        if (
          this.processDetails &&
          this.processDetails.parentJobId &&
          ExtractionUtility.isValidGuid(this.processDetails.parentJobId) &&
          this.processDetails.state ===
            StateDescriptions.FILE_DRAFT_BEFORE_EXTRACTION &&
          this.documents.length > 0 &&
          !hasErrors && this.processDetails.extractionType != DataIngestionConstants.SpecificKPI
        ) {
          this.s3UploadResponse = [];
          this.documents.forEach((doc) => {
            this.s3UploadResponse.push({
              documentId: doc.id,
              url: doc.url,
              processId: this.processId,
            } as UploadResponse);
          });
          const { extractedModel, fileFormat } =
            ExtractionUtility.processS3Response(this.s3UploadResponse);
          this.s3UrlExtractedModel = extractedModel;
          this.s3UrlExtractedModel.forEach((item) => {
            item.statusId = this.processDetails.statusId;
            item.parentJobId = this.processDetails.parentJobId;
          });
          this.fileFormatModel = fileFormat;
          if (!this.processDetails.isClassifiers) {
            this.isLoader = true;
            this.startJobStatusPolling(this.processDetails.parentJobId, () => {
              this.getFsClassifier(this.processDetails.parentJobId);
            });
          }else{
            this.FetchClassifier();
          }
        }
      });
    }
  }

  /**
   * Sets company initials based on company name
   */
  initializeCompanyInitials(): void {
    const InitialsName=this.processDetails.extractionType==DataIngestionConstants.SpecificKPI?
    this.processDetails.fundName:this.processDetails.companyName;
    this.companyInitials = DataIngestionUtility.extractCompanyInitials(
      InitialsName
    );
  }

  /**
   * Angular lifecycle hook for component destruction
   * Unsubscribes from observables to prevent memory leaks
   */
  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.stopJobStatusPolling();
  }

  /**
   * Handles file selection event from the file input
   * Validates and processes selected files
   * @param event The file selection event
   */
  onFileSelected(event: any): void {
    try {
      this.fileErrors = [];
      const files = this.getFilesFromEvent(event);
      if (!this.validateFileSelection(files)) return;

      if (!this.validateTotalFileCount(files)) {
        this.showMaxFilesWarning();
        return;
      }

      const processedFiles = this.processNewFiles(files);
      this.updateFileCollections(processedFiles);
      if (
        this.processDetails.parentJobId == ExtractionUiConstants.EMPTY_GUID ||
        this.documents.length == 0
      ) {
        this.stopJobStatusPolling()
        this.updateUIState();
        this.uploadFiles(this.processId);
      }
    } catch (error) {
      this.handleFileSelectionError(error);
    } finally {
      this.clearFileInput();
    }
  }

  /**
   * Extracts File objects from an event
   * @param event The file input change event
   * @returns Array of File objects
   */
  private getFilesFromEvent(event: Event): File[] {
    const input = event.target as HTMLInputElement;
    return Array.from(input.files || []);
  }

  /**
   * Validates that at least one file was selected
   * @param files Array of files to validate
   * @returns Boolean indicating if file selection is valid
   */
  private validateFileSelection(files: File[]): boolean {
    return files.length > 0;
  }

  /**
   * Validates that the total number of files doesn't exceed the maximum limit
   * @param files Array of newly selected files
   * @returns Boolean indicating if total file count is valid
   */
  private validateTotalFileCount(files: File[]): boolean {
    const totalExistingFiles = this.selectedFilesList.length;
    const potentialTotalFiles = totalExistingFiles + files.length;
    return potentialTotalFiles <= this.fileConfig.maxFiles;
  }

  /**
   * Shows a warning toast when maximum file count is exceeded
   */
  private showMaxFilesWarning(): void {
    this.toasterService.warning(
      DataIngestionConstants.MAX_FILES_MESSAGE(this.fileConfig.maxFiles),
      DataIngestionConstants.EMPTY_TITLE,
      { positionClass: DataIngestionConstants.ToasterMessagePosition }
    );
  }

  /**
   * Processes newly selected files, checking for duplicates and validating formats
   * @param files Array of newly selected files
   * @returns Array of processed SelectedFile objects
   */
  private processNewFiles(files: File[]): SelectedFile[] {
    const existingFileNames = new Set([
      ...this.selectedFiles.map((f) => f.name),
      ...this.backupUploadedFileNames.map((f) => f.name),
    ]);

    const { newFiles } = DataIngestionUtility.processFiles(
      files,
      this.fileConfig,
      existingFileNames
    );

    return newFiles;
  }

  /**
   * Updates file collections with newly processed files
   * @param newFiles Array of newly processed files to add
   */
  private updateFileCollections(newFiles: SelectedFile[]): void {
    const newFileDetails = newFiles.map((f) => ({ id: f.id, name: f.name }));

    this.backupUploadedFiles.push(...newFiles);
    this.backupUploadedFileNames.push(...newFileDetails);
    this.selectedFilesList.push(...newFiles);
    this.selectedFiles.push(...newFileDetails);
  }

  /**
   * Handles errors that occur during file selection and processing
   * @param error The error that occurred
   */
  private handleFileSelectionError(error: unknown): void {
    console.error("Error processing files:", error);
    this.toasterService.error(
      "An error occurred while processing files",
      DataIngestionConstants.EMPTY_TITLE,
      { positionClass: DataIngestionConstants.ToasterMessagePosition }
    );
  }

  /**
   * Clears the file input element and backup file lists
   */
  clearFileInput(): void {
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = DataIngestionConstants.EMPTY_TITLE;
      this.backupUploadedFileNames = [];
      this.backupUploadedFiles = [];
    }
  }

  /**
   * Sorts files by status and name
   * @param ascending Whether to sort in ascending order (default: true)
   */
  sortFilesByStatusAndName(ascending: boolean = true): void {
    this.selectedFilesList = DataIngestionUtility.sortFilesByStatusAndName(
      this.selectedFilesList,
      ascending
    );
  }

  /**
   * Initializes the form group for the file grid
   */
  initForm(): void {
    this.gridForm = this.fb.group({
      files: this.fb.array([]),
    });
  }

  /**
   * Getter for accessing the files FormArray
   * @returns FormArray of file entries
   */
  get formArray(): FormArray {
    return this.gridForm.get("files") as FormArray;
  }

  /**
   * Updates the form array to match the current selectedFilesList
   */
  updateFormArray(): void {
    this.assignPageConfigModuleNames();
    const formArray = this.gridForm.get("files") as FormArray;
    formArray.clear();
    this.selectedFilesList.forEach((file) => {
      const pagesArray = this.fb.array(
        this.modules.map((page) => {
          const itemsControl = this.fb.control("", [
            this.commaSeparatedIntegersValidator(), // Optional validation
          ]);
          
          return this.fb.group({
            moduleId: [page.moduleId],
            moduleName: [page.moduleName],
            alias: [page.alias],
            items: itemsControl,
          });
        })
      );
      const group = this.fb.group({
        id: [file.id],
        name: [file.name],
        documentType: [file.documentType, Validators.required],
        pages: pagesArray,
        selected: [false],
        periodType: [null],
        year: [null],
        month: [null],
        quarter: [null]
      });

      // Add custom validator to ensure at least one valid item in pagesArray
      if (this.processDetails.extractionType != DataIngestionConstants.SpecificKPI)
        group.setValidators(this.atLeastOnePageItemValidator(pagesArray));

      formArray.push(group);
    });

    this.gridForm.updateValueAndValidity();
  }
  private commaSeparatedIntegersValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
      const value = control.value?.trim();

      // Skip validation if the field is empty (optional field)
      if (!value) {
        return null;
      }

      // Validate comma-separated integers between 1 and 200
      const items = value.split(",").map((item) => item.trim());
      const isValid = items.every((item) => {
        const number = parseInt(item, 10);
        return !isNaN(number) && number >= 1 && number <= 200;
      });

      return isValid ? null : { invalidCommaSeparatedIntegers: true };
    };
  }
  /**
   * Custom validator to ensure at least one item in the pages array has a value
   * @param pagesArray The FormArray to validate
   * @returns A validation function
   */
  private atLeastOnePageItemValidator(pagesArray: FormArray) {
    return (): { [key: string]: boolean } | null => {
      const hasValue = pagesArray.controls.some((group) => {
        const itemsControl = group.get("items");
        const value = itemsControl?.value?.trim();
        return (
          value && value !== "" && this.isValidCommaSeparatedIntegers(value)
        );
      });

      const hasInvalidComma = pagesArray.controls.some((group) => {
        const itemsControl = group.get("items");
        const value = itemsControl?.value?.trim();
        return value && (value.endsWith(",") || value.startsWith(","));
      });

      if (hasInvalidComma) {
        return { endsWithComma: true };
      }

      return hasValue ? null : { atLeastOnePageItemRequired: true };
    };
  }
  /**
   * Validates if a string contains only comma-separated integers between 1 and 200
   * @param value The string to validate
   * @returns Boolean indicating if the string is valid
   */
  private isValidCommaSeparatedIntegers(value: string): boolean {
    const items = value.split(",").map((item) => item.trim());
    return items.every((item) => {
      const number = parseInt(item, 10);
      return !isNaN(number) && number >= 1 && number <= 200;
    });
  }

  /**
   * Handles document type change for a specific file
   * @param value The new document type value
   * @param rowIndex The index of the file in the array
   */
  onDocumentTypeChange(value: any, rowIndex: number): void {
    const pathToFind = typeof value === 'object' ? value.id?.toString() : value?.toString();
    this.updateDocumentStructure(pathToFind);
    const currentDocumentType = this.selectionForm.get('documentType').value;
      this.selectionForm.patchValue({
        documentType: currentDocumentType,
        periodType: null,
        year: null,
        month: null,
        quarter: null
      });
    this.selectionForm.markAsTouched();
  }
  /**
   * Removes a file from the form array at the specified index
   */
  private removeFileFromFormArray(index: number): void {
    const formArray = this.gridForm.get("files") as FormArray;
    formArray.removeAt(index);
  }

  /**
   * Removes a file from the selected files lists
   */
  private removeFileFromLists(index: number, id: string): void {
    // Remove from selectedFilesList
    this.selectedFilesList.splice(index, 1);

    // Remove from selectedFiles
    const selectedFileIndex = this.selectedFiles.findIndex(
      (file) => file.id === id
    );
    if (selectedFileIndex !== -1) {
      this.selectedFiles.splice(selectedFileIndex, 1);
    }
  }

  /**
   * Removes a document from the documents array
   */
  private removeDocumentFromList(id: string): void {
    const docIndex = this.documents.findIndex((doc) => doc.id === id);
    if (docIndex !== -1) {
      this.documents.splice(docIndex, 1);
    }
  }

  /**
   * Deletes a file at the specified index
   */
  deleteFile(index: number, id: string): void {
    this.removeFileFromFormArray(index);

    const document = this.documents.find((doc) => doc.id === id);
    if (document) {
      this.deleteDocumentFile(document.id, document.s3Path);
      this.removeFileFromLists(index, id);
      this.removeDocumentFromList(id);
    } else {
      if (
        this.deleteDocumentFile(
          this.s3UploadResponse[0].documentId,
          this.s3UploadResponse[0].url
        )
      ) {
        this.removeFileFromLists(index, id);
      } else if (
        !this.deleteDocumentFile(
          this.s3UploadResponse[0].documentId,
          this.s3UploadResponse[0].url
        )
      ) {
        this.removeFileFromLists(index, id);
      }
    }

    this.container.hide();
    this.updateFormArray();
  }

  /**
   * Counts the number of invalid files in the provided array
   * @param files Array of files to check
   * @returns Number of invalid files
   */
  countInvalidFiles(files: SelectedFile[]): number {
    return files.filter(
      (file) => file.status === DataIngestionConstants.INVALID
    ).length;
  }

  /**
   * Updates the UI state by sorting files and refreshing the form array
   */
  updateUIState(): void {
    this.updateFormArray();
    this.handleFileErrors();
  }

  /**
   * Handles displaying errors for invalid files
   */
  handleFileErrors(): void {
    const invalidFileCount = this.countInvalidFiles(this.selectedFilesList);
    if (invalidFileCount > 0) {
      this.toasterService.error(
        DataIngestionConstants.FILES_NOT_UPLOADED(invalidFileCount),
        DataIngestionConstants.EMPTY_TITLE,
        { positionClass: DataIngestionConstants.ToasterMessagePosition }
      );
    }
  }

  /**
   * Shows file details in a popover when a file row is clicked
   * @param event The click event
   * @param dataItem The file data item
   */
  showFileDetails(event: any, dataItem: any): void {
    this.selectedFileErrors = dataItem.errors || [];
    const anchor = event.target;
    this.container.hide();
    this.container.show(anchor);
  }

  /**
   * Closes the file details popover
   */
  onClose(): void {
    this.container.hide();
    this.selectedFileErrors = [];
  }

  /**
   * Gets the appropriate icon for a file based on its extension
   * @param fileName Name of the file
   * @returns SVG icon for the file type
   */
  getFileIcon(fileName: string): SVGIcon {
    return DataIngestionUtility.getFileIcon(fileName);
  }

  /**
   * Fetches portfolio company details using the encrypted company ID
   */
  getPortfolioCompanies() {
    if (!this.companyEncryptedID) return;
    this.portfolioCompanyService
      .getPortfolioCompanyLogo({ Value: this.companyEncryptedID })
      .subscribe({
        next: (result: PortfolioLogoModel) => {
          this.handleCompanyLogo(result);
        },
        error: (error) => {
          this.isLogo = false;
        },
      });
  }

  /**
   * Processes company logo information from field value list
   * @param fieldValueList List of field values from the company
   */
  handleCompanyLogo(companyModel: PortfolioLogoModel): void {
    if (!companyModel?.imagePath) {
      this.isLogo = false;
      this.companyLogo = null;
      return;
    }
    this.companyLogo = companyModel.imagePath.trim();
    this.isLogo = this.companyLogo !== "NA" && this.companyLogo !== "";
  }
  filterDocumentTypes(searchValue: string): void {
    this.documentTypes = this.sourceDocumentTypes.filter((documentType) =>
      documentType.documentName
        .toLowerCase()
        .includes(searchValue.toLowerCase())
    );
  }
  isDocumentTypeDisabled(file: any): boolean {
    return file.status === "invalid";
  }
  
  /**
   * Gets the pages from form array for a specific file based on rowIndex
   * @param rowIndex The index of the file in the form array
   * @returns An array of page objects with moduleId, moduleName, and items
   */
  getPagesForFile(rowIndex: number): any[] {
    if (!this.formArray || rowIndex < 0 || rowIndex >= this.formArray.length) {
      return [];
    }
    
    const fileGroup = this.formArray.at(rowIndex);
    if (!fileGroup) {
      return [];
    }
    
    const pagesArray = fileGroup.get('pages') as FormArray;
    if (!pagesArray) {
      return [];
    }
    
    return pagesArray.controls.map(control => {
      const group = control as FormGroup;
      return {
        moduleId: group.get('moduleId')?.value,
        moduleName: group.get('moduleName')?.value,
        items: group.get('items')?.value
      };
    });
  }  
  extract() {
    this.updateMappingDetails();
    if(this.processDetails.extractionType == DataIngestionConstants.SpecificKPI){
      this.add_update_job={
        parentJobId: this.processDetails.parentJobId,
        processId: this.processId,
        tenantId: "00000000-0000-0000-0000-000000000001",
        jobId: this.processDetails.jobId,
        statusId: ExtractionUtility.getIdByStatusName(
          StateDescriptions.EXTRACTION_IN_PROGRESS,
          this.stateAndStatus
        ),
      }
      this.extractionSharedService
      .post(ExtractionUiConstants.Job_CREATE_UPDATE, this.add_update_job)
      .subscribe({
        next: (result: any) => {
          this.router.navigate(["data-ingestion"]);
        },
        error: (error) => {
          this.handleApiError(error, "create Job");
        },
      });
    }
    else{
      if (this.classifierData && this.classifierData.tables) {
        if (Array.isArray(this.classifierData.tables)) {
          const othersTable = this.classifierData.tables.find(
            (t: any) => t.label === "Others"
          );
          if (othersTable && Array.isArray(othersTable.suggestions)) {
            othersTable.suggestions = [];
          }
        }
      }
      this.initiateFinalDocIngestion();
    }
  }
  setPdfSource(dataItem: any,rowIndex:number) {
    let pages = this.getPagesForFile(rowIndex)
    this.pdfSource = {
      fileName: dataItem.name,
      fileUrl: "",
      fileType: dataItem.fileType,
      processId: this.processId,
      pageList:pages
    };
    this.showPreview = true;
  }
  uploadFiles(processId: string) {
    const formData = ExtractionUtility.uploadNewFilesFormData(
      this.selectedFilesList,
      this.formArray,
      processId,
      this.processDetails.extractionType
    );
    this.isLoader = true;
    const uri = ExtractionUiConstants.EXTRACT_UPLOAD;
    this.extractionSharedService.post(uri, formData).subscribe({
      next: (result: UploadResponse[]) => {
        this.s3UploadResponse = result;
        if (this.s3UploadResponse.length > 0) {
          const { extractedModel, fileFormat } =
            ExtractionUtility.processS3Response(this.s3UploadResponse);
          this.s3UrlExtractedModel = extractedModel;
          this.s3UrlExtractedModel.forEach((item) => {
            item.statusId = ExtractionUtility.getIdByStatusName(
              StateDescriptions.FILE_DRAFT_BEFORE_EXTRACTION,
              this.stateAndStatus
            );
          });
          this.fileFormatModel = fileFormat;
          this.createDocIngestionFetchFile();
        }
      },
      error: (error) => {
        this.isLoader = false;
        this.s3UploadResponse = [];
      },
    });
  }
  showToast(
    message: string,
    type: "success" | "error" | "warning" | "info"
  ): void {
    this.toasterService[type](message, DataIngestionConstants.EMPTY_TITLE, {
      positionClass: DataIngestionConstants.ToasterMessagePosition,
    });
  }
  deleteDocumentFile(documentId: string, key: string): boolean {
    const uri = ExtractionUiConstants.EXTRACT_FILE_DELETE;
    this.extractionSharedService
      .post(uri, { id: documentId, key: key })
      .subscribe({
        next: (result: boolean) => {
          if (result) {
            this.showToast("File deleted successfully", "success");
            this.stopJobStatusPolling();
            return true;
          }
        },
        error: (error) => {
          this.showToast("Error deleting file", "error");
          return false;
        },
      });
    return false;
  }
  handleApiError(error: any, operation: string = "API operation"): void {
    this.isLoader = false;
    console.error(`${operation} failed:`, error);
  }
  createDocIngestionFetchFile() {
    this.extractionSharedService
      .postWithApiKey(
        ExtractionUiConstants.DOC_INGEST,
        ExtractionUtility.createIngestConfigurationModel(
          this.fileFormatModel,this.config.client_env,
          this.processDetails
        )
      )
      .subscribe({
        next: (result: IngestionResponseModel) => {
          this.spread_fs_start_Model = <IngestionStartModel>{};
          this.spread_fs_start_Model.files = result.files;
          this.s3UrlExtractedModel.forEach((item) => {
            item.parentJobId = result.job_id;
          });
          this.ingestionStart(result.job_id, this.spread_fs_start_Model);
        },
        error: (error) => {
          this.handleApiError(error, "ingestion start");
        },
      });
  }
  ingestionStart(job_id: string, spread_fs_start: IngestionStartModel) {
    this.extractionSharedService
      .postWithApiKey(
        ExtractionUiConstants.SPREAD_FS_START(job_id),
        spread_fs_start
      )
      .subscribe({
        next: (result: any) => {
          this.createUpdateJob();
          this.startJobStatusPolling(this.add_update_job.parentJobId, () => {
            this.getFsClassifier(this.add_update_job.parentJobId);
          });
        },
        error: (error) => {
          this.handleApiError(error, "ingestion start");
        },
      });
  }
  getFsClassifier(parentJobId: string) {
    this.extractionSharedService
      .getWithApiKey(ExtractionUiConstants.SPREAD_FS_CLASSIFIER(parentJobId))
      .subscribe({
        next: (response: TableSuggestionResponse) => {
          this.stopJobStatusPolling();
          this.classifierData = response;
          this.tablePageNumbers =
            TableSuggestionsUtility.getAllFilePageDetails(response);
          this.isLoader = false;
          this.updateFormWithSuggestedPages(this.classifierData);
          this.addOrUpdateClassifier();
        },
        error: (error) => {
          this.handleApiError(error, "classifier");
        },
      });
  }
  createUpdateJob() {
    this.add_update_job = ExtractionUtility.mapModelProperties<CreateJobs>(
      this.s3UrlExtractedModel[0],
      ExtractionUiConstants.Job_Properties
    );
    this.extractionSharedService
      .post(ExtractionUiConstants.Job_CREATE_UPDATE, this.add_update_job)
      .subscribe({
        next: (result: any) => {},
        error: (error) => {
          this.handleApiError(error, "create Job");
        },
      });
  }
  startJobStatusPolling(parentJobId: string, onComplete?: () => void): void {
    if (this.statusCheckSubscription) {
      this.statusCheckSubscription.unsubscribe();
    }
    this.statusCheckSubscription = interval(this.POLLING_INTERVAL).subscribe(
      () => {
        this.checkJobStatus(parentJobId, onComplete);
      }
    );
  }
  showErrorToast(message: string): void {
    this.toasterService.error(
      message,
      DataIngestionConstants.EMPTY_TITLE,
      { positionClass: DataIngestionConstants.ToasterMessagePosition }
    );
  }

  checkJobStatus(parentJobId: string, onComplete?: () => void): void {
    this.extractionSharedService
      .getWithApiKey(ExtractionUiConstants.JOB_ID_STATUS(parentJobId))
      .subscribe({
        next: (response: any) => {
          if (response.status === this.COMPLETED_STATUS) {
            this.stopJobStatusPolling();
            if (onComplete) {
              onComplete();
            }
          }
          if( response.status === this.FAILED_STATUS) {
            this.s3UrlExtractedModel.forEach((item) => {
              item.statusId = ExtractionUtility.getIdByStatusName(
                StateDescriptions.API_FAILED,
                this.stateAndStatus
              );
            });
            this.createUpdateJob();
             this.stopJobStatusPolling();
             this.showErrorToast(DataIngestionConstants.API_ERROR);
             this.router.navigate(["data-ingestion"]);
          }
        },
        error: (error) => {
          this.stopJobStatusPolling();
        },
      });
  }
  stopJobStatusPolling(): void {
    if (this.statusCheckSubscription) {
      this.statusCheckSubscription.unsubscribe();
      this.statusCheckSubscription = null;     
    }
    this.isLoader = false;
  }
  updateFormWithSuggestedPages(response: TableSuggestionResponse): void {
    if (!this.formArray || !response.tables) return;
    this.formArray.controls.forEach((fileGroup,fileIndex) => {
      const pages = fileGroup.get("pages") as FormArray;
      if (!pages) return;
      pages.controls.forEach((pageGroup, pageIndex) => {
        const moduleName = pageGroup.get("moduleName")?.value;
        if (!moduleName) return;
        const tableSuggestions = response.tables.find(
          (table) => table.label.toLowerCase() === moduleName.toLowerCase()
        );
        if (tableSuggestions?.suggestions) {
          const suggestedPages = [...tableSuggestions.suggestions]
          .sort((a, b) => b.score - a.score)  // descending order
          .slice(0, 2)  // take top 2
          .map(suggestion => suggestion.page)
          .sort((a, b) => a - b);  // sort pages numerically
          if (suggestedPages.length > 0) {
            pageGroup.get("items")?.setValue(suggestedPages.join(","));
            pageGroup.get("items")?.markAsTouched();            
           this.onChangeAddOrUpdateClassifier(moduleName, suggestedPages.join(","), fileIndex);
          }
        }
      });
    });
  }
  initiateFinalDocIngestion() {
    this.isLoader = true;
    this.extractionSharedService
      .postWithApiKey(
        ExtractionUiConstants.FINAL_DOC_INGEST,
        ExtractionUtility.createIngestConfigurationModel(
          this.fileFormatModel,this.config.client_env,
          this.processDetails
        )
      )
      .subscribe({
        next: (result: IngestionResponseModel) => {
          this.s3UrlExtractedModel.forEach((item) => {
            item.jobId = result.job_id;
          });
          this.processDocumentExtraction(result.job_id, this.classifierData);
        },
        error: (error) => {
          this.handleApiError(error, "doc extraction");
        },
      });
  }

  processDocumentExtraction(job_id: string, fs_response: TableSuggestionResponse) {
    this.extractionSharedService
      .postWithApiKey(
        ExtractionUiConstants.EXTRACT_DOC_EXTRACTION(job_id),
        fs_response
      )
      .subscribe({
        next: (result: any) => {
          this.s3UrlExtractedModel.forEach((item) => {
            item.statusId = ExtractionUtility.getIdByStatusName(
              StateDescriptions.EXTRACTION_IN_PROGRESS,
              this.stateAndStatus
            );
          });

          if (result.status == StatusConstants.RUNNING) {
            this.createUpdateJob();
            this.addOrUpdateClassifier();
            this.router.navigate(["data-ingestion"]);
          }
          this.isLoader = false;
        },
        error: (error) => {
          this.handleApiError(error, "doc extraction");
        },
      });
  }
  addOrUpdateClassifier() {
    if (this.classifierData.tables.length == 0) return;
    this.extractionSharedService
      .post(
        ExtractionUiConstants.AddOrUpdateClassifier(this.processId),
        this.classifierData
      )
      .subscribe({
        next: (result: any) => {},
      });
  }
  onPagesInputChange(event: any, rowIndex: number, pageIndex: number): void {
    const filesArray = this.gridForm.get("files") as FormArray;
    const fileGroup = filesArray.at(rowIndex) as FormGroup;
    const pagesArray = fileGroup.get("pages") as FormArray;
    const pageGroup = pagesArray.at(pageIndex) as FormGroup;
    const moduleName = pageGroup.get("moduleName")?.value;
    const newValue = event.target.value || "";
    pageGroup.get("items").setValue(newValue);
    pageGroup.get("items").markAsTouched();
    this.onChangeAddOrUpdateClassifier(moduleName, newValue, rowIndex);
  }
  onChangeAddOrUpdateClassifier(
    moduleName: string,
    newValue: any,
    rowIndex: number
  ): void {
    if (this.classifierData && this.selectedFilesList[rowIndex]) {
      const existingPages = [];
      const tableType = this.classifierData.tables.find(
        (t) => t.label === moduleName
      );
      if (tableType) {
        tableType.suggestions.forEach((s) => {
          existingPages.push(s.page);
        });
      }
      const newPages = TableSuggestionsUtility.parsePageString(newValue);

      if (
        JSON.stringify(existingPages.sort()) !== JSON.stringify(newPages.sort())
      ) {
        // Find pages to remove (pages that exist in existingPages but not in newPages)
        const pagesToRemove = existingPages.filter(
          (page) => !newPages.includes(page)
        );

        // Find pages to add (pages that exist in newPages but not in existingPages)
        const pagesToAdd = newPages.filter(
          (page) => !existingPages.includes(page)
        );

        // Only remove pages that are no longer needed
        if (pagesToRemove.length > 0) {
          this.classifierData =
            TableSuggestionsUtility.removeTableSuggestionPages(
              this.classifierData,
              moduleName,
              pagesToRemove,
              null
            );
        }

        // Only add pages that are newly added
        if (pagesToAdd.length > 0) {
          this.classifierData = TableSuggestionsUtility.addTableSuggestionPages(
            this.classifierData,
            moduleName,
            pagesToAdd,
            this.s3UploadResponse[0].documentId,
            0.0
          );
        }
      }
    }
  }
  FetchClassifier() {
    this.isLoader = true;
    this.extractionSharedService
      .get(ExtractionUiConstants.FetchClassifier(this.processId))
      .subscribe({
        next: (response: TableSuggestionResponse) => {
          this.isLoader = false;
          if(response.tables.length > 0){            
             this.classifierData = response;
            this.updateFormWithSuggestedPages(this.classifierData);
          }
        },
        error: (error) => {
          this.isLoader = false;
        }
      });
  }
  @HostListener('window:message', ['$event'])
  onMessage(event: MessageEvent) {
    // Extract the origin from the previewUrl for proper comparison
    // const expectedOrigin = new URL(environment.ingestion_url).origin;
    const expectedOrigin=window.location.origin;
    // Check if the message is coming from the expected origin
    if (event.origin !== expectedOrigin) {
      console.log('Message received from unexpected origin:', event.origin);
      return;
    }
    
    // Process the message data
    if (event.data && typeof event.data === 'object') {
      // Handle the received data based on message type
      if (event?.data?.type === 'pdf-data-response' && event?.data?.modules?.length > 0) {
        // Handle the response data
        this.showPreview =  false;
        this.pdfSource = null;
        this.handlePdfDataResponse(event.data.modules);
      }
    }
  }
  /**
   * Handles the response from the iframe after sending PDF data
   * @param data The data received from the iframe
   */
  handlePdfDataResponse(modules: any[]): void {
    const fileGroup = this.formArray.at(0) as FormGroup;
    const pagesArray = fileGroup.get("pages") as FormArray;

    modules.forEach((module) => {
      const pageGroup = pagesArray.controls.find((control) => {
        const group = control as FormGroup;
        return group.get("moduleId")?.value === module.moduleId;
      }) as FormGroup;

      if (pageGroup) {
        const pageNumbers = module.pages?.length
          ? module.pages.join(",")
          : module.items || "";

        if (pageNumbers) {
          pageGroup.get("items")?.setValue(pageNumbers);
          pageGroup.get("items")?.markAsDirty();
          pageGroup.get("items")?.markAsTouched();
          const moduleName = pageGroup.get("moduleName")?.value;
          if (moduleName) {
            this.onChangeAddOrUpdateClassifier(moduleName, pageNumbers, 0);
          }
        }
      }
    });

    this.gridForm.markAsDirty();
    this.gridForm.updateValueAndValidity();
  }
  loadModules(){
    this.dataIngestion.getKpiModulesPageConfigDetails().subscribe({
      next: (result: any) => {        
        if (result.length>0) {
          this.pageConfigModel = result;
        }
      },
      error: (error) => {},
    });
  }
  assignPageConfigModuleNames() {
    this.modules = this.modules.map((item) => {
      const filterItems = this.pageConfigModel.filter((module) => module.moduleId === item.moduleId);
      // Return modified item when match found, otherwise return original item
      return filterItems.length > 0 
        ? { ...item, alias: filterItems[0]["pageConfigAliasName"] || item.moduleName }
        : item;
    });
  }
  getFileExtensions(name:string): string{
    return DataIngestionUtility.getFileExtension(name);
  }
  specificKpiJobStart(){
    this.extractionIngestionService.specificKpiExtract(this.processDetails.processId).subscribe(response => {
      this.router.navigate(["data-ingestion"]);
    });
  }
  loadDocumentTypes() {
   if(this.processDetails.extractionType === DataIngestionConstants.AsIsExtraction) {
    if (this.processDetails.encryptedPortfolioCompanyId) {
      this.repositoryConfigService.getRepositoryStructureData(this.processDetails.encryptedPortfolioCompanyId, FeaturesEnum.PortfolioCompany).subscribe(this.repositoryResponseHandler());
    }
  } else if(this.processDetails.extractionType === DataIngestionConstants.SpecificKPI) {
    this.repositoryConfigService.getRepositoryStructureData(this.processDetails.encryptedFundId, FeaturesEnum.Fund).subscribe(this.repositoryResponseHandler());
  }
}

  private repositoryResponseHandler() {
    return {
      next: (response: RepositoryNode) => {
        this.processRepositoryStructure(response);
        this.updateDocumentTypes();
      },
      error: (error) => {
        console.error('Error fetching repository structure:', error);
      }
    };
  }

private processRepositoryStructure(response: any) {
  if (!response?.data || !Array.isArray(response.data)) return;
  this.repositoryData = response.data;
  this.documentTypeMap.clear();
  response.data.forEach(rootNode => {
      if (rootNode?.name && rootNode?.path) {
          this.documentTypeMap.set(rootNode.path, rootNode.name);
      }
  });
  this.updateDocumentTypes();
}
private updateDocumentTypes() {
this.documentTypes = Array.from(this.documentTypeMap.entries())
    .map(([path, name]) => ({
        id: Number(path), 
        documentName: name
    }))
    .sort((a, b) => a.documentName.localeCompare(b.documentName)); 
this.sourceDocumentTypes = [...this.documentTypes];
}
initSelectionForm(): void {
  this.selectionForm = this.fb.group({
    documentType: [null, Validators.required],
    periodType: [null, Validators.required],
    year: [null],
    month: [null],
    quarter: [null]
  });
}
areAllDocumentsSelected(): boolean {
  if (!this.formArray || this.formArray.length === 0) {
    return false;
  }
  return this.formArray.controls.every((group, index) => {
    if (this.selectedFilesList[index]) {
      this.selectedFilesList[index].selected = group.get('selected').value;
    }
    return group.get('selected').value === true;
  });
}
toggleAllDocumentsSelection(): void {
  if (!this.formArray || this.formArray.length === 0) {
    return;
  }
  
  const selectAll = !this.areAllDocumentsSelected();
  this.formArray.controls.forEach((group, index) => {
    group.get('selected').setValue(selectAll);
    group.get('selected').markAsDirty();
    if (this.selectedFilesList[index]) {
      this.selectedFilesList[index].selected = selectAll;
    }
  });
  this.gridForm.updateValueAndValidity();
  this.updateSelectionStatus();
}

toggleDocumentSelection(file: any, rowIndex: number): void {
  if (!this.formArray || rowIndex < 0 || rowIndex >= this.formArray.length) {
    return;
  }  
  const formGroup = this.formArray.at(rowIndex);
  const currentValue = formGroup.get('selected').value;
  formGroup.get('selected').setValue(!currentValue);
  formGroup.get('selected').markAsDirty();
  if (this.selectedFilesList[rowIndex]) {
    this.selectedFilesList[rowIndex].selected = !currentValue;
  }
  this.gridForm.updateValueAndValidity();
  this.updateSelectionStatus();
}

updateSelectionStatus(): void {
  const selectedDocuments = this.formArray 
    ? this.formArray.controls.filter(group => group.get('selected').value === true).length 
    : 0;
  const hasAnySelection = selectedDocuments > 0;
  this.showSelectionPopup = hasAnySelection;
  if (!hasAnySelection) {
    this.initSelectionForm();
  }
}

closeSelectionPopup(): void {
  this.formArray.controls.forEach((control, index) => {
    if (control.get('selected').value) {
      control.get('selected').setValue(false);
      this.selectedFilesList[index].selected = false;
    }
  });
  this.initSelectionForm();
  this.showSelectionPopup = false;
}
updateDocumentStructure(pathToFind: string): void {
  const documentStructure = this.repositoryData.find(node => node.path === pathToFind); 
  if (documentStructure) {   
      if (documentStructure.children) {
          this.availableYears = documentStructure.children
              .map(year => ({
                  id: year.path,
                  name: year.name,
                  text: year.name
              }))
              .sort((a, b) => b.name.localeCompare(a.name));
          if (this.availableYears.length > 0) {
              this.isPeriodSelectionEnabled = true;
              this.determineAvailablePeriodTypes(documentStructure);
          }
      }
      else{
        const periodTypeControl = this.selectionForm.get('periodType');
        periodTypeControl.clearValidators();
        periodTypeControl.updateValueAndValidity();
      }
  }

}

determineAvailablePeriodTypes(structure: any): void {
  this.periodTypes = ExtractionUtility.determineAvailablePeriodTypes(structure);
}
onPeriodTypeChange(periodTypeValue: { id: string; name: string }): void {
  const yearControl = this.selectionForm.get('year');
  const monthControl = this.selectionForm.get('month');
  const quarterControl = this.selectionForm.get('quarter');
  yearControl.clearValidators();
  monthControl.clearValidators();
  quarterControl.clearValidators();
  if (periodTypeValue.id === 'year') {
    yearControl.setValidators([Validators.required]);
  } else if (periodTypeValue.id === 'month') {
    yearControl.setValidators([Validators.required]);
    monthControl.setValidators([Validators.required]);
  } else if (periodTypeValue.id === 'quarter') {
    yearControl.setValidators([Validators.required]);
    quarterControl.setValidators([Validators.required]);
  }  
  yearControl.updateValueAndValidity();
  monthControl.updateValueAndValidity();
  quarterControl.updateValueAndValidity();
}
onYearChange(value: any): void {
  const yearPath = typeof value === 'object' ? value.id?.toString() : value?.toString();
  this.handleBulkYearUpdate(yearPath);
}
private handleBulkYearUpdate(yearPath: string): void {
  const documentStructure = this.repositoryData.find(node => node.path === this.selectionForm.get('documentType').value.id.toString());
  this.updateAvailableOptions(documentStructure, yearPath);
}
 
private updateAvailableOptions(documentStructure: any, yearPath: string): void {
  const yearNode = documentStructure.children?.find(year => year.path === yearPath);
  this.availableMonths = [];
  this.availableQuarters = []; 
  if (yearNode?.children) {
      this.availableMonths = yearNode.children
      .filter(node => !node.name.includes('Q'))
      .map(month => {
          const monthOption = CommonConstants.monthOptions.find(
              opt => month.name.startsWith(opt.value)
          );          
          return {
              id: month.path,
              name: month.name.replace(yearNode.name, '').trim(),
              number: monthOption?.number || 0
          };
      })
      .sort((a, b) => (a.number || 0) - (b.number || 0));
      this.availableQuarters = yearNode.children
          .filter(node => node.name.includes('Q'))
          .map(quarter => ({
              id: quarter.path,
              name: quarter.name,
              text: `${quarter.name.replace(yearNode.name, '').trim()}`
          }))
          .sort((a, b) => a.name.localeCompare(b.name));
  }
}
applyBulkChanges(): void {
  if (!this.selectionForm.valid) {
    return;
  }
  this.formArray.controls.forEach((control, index) => {
    if (control.get('selected').value) {
       control.get('documentType').setValue(this.selectionForm.get('documentType').value);
       control.get('periodType').setValue(this.selectionForm.get('periodType').value);
       control.get('year').setValue(this.selectionForm.get('year').value);
       control.get('month').setValue(this.selectionForm.get('month').value);
       control.get('quarter').setValue(this.selectionForm.get('quarter').value);
       control.get('selected').setValue(false);
    }
  });
  this.initSelectionForm();
  this.showSelectionPopup = false;
}
updateMappingDetails(){
  const fileConfigurationDetails = this.gridForm.value.files.map(file => (<FileConfigurationDetails>{
    fileId: file.id,
    documentTypeId: file.documentType.id,
    documentType: file.documentType.documentName,
    periodType: file.periodType?.name,
    year: file?.year?.text,
    month: file?.month?.name,
    quarter: file?.quarter?.text,
  }));

  this.extractionSharedService.post(ExtractionUiConstants.UPDATE_FILE_CONFIGURATIONS, fileConfigurationDetails).subscribe({ 
    next: (result: any) => {
      this.showToast('File configurations updated successfully', 'success');
    },
    error: (error) => {
      this.showToast('Error updating file configurations', 'error');
    },
  });

  
}
}