import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { BehaviorSubject, of } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { DataIngestionFetchFileComponent } from './data-ingestion-fetchfile.component';
import { FileSharingService } from 'src/app/services/file-sharing.service';
import { DataIngestionService } from 'src/app/services/data-ingestion.service';
import { PortfolioLogoModel, SelectedFile } from '../data-ingestion/data-ingestion.model';
import { KendoModule } from 'src/app/custom-modules/kendo.module';
import { PortfolioCompanyService } from 'src/app/services/portfolioCompany.service';
import { ExtractionSharedService } from 'src/app/services/extraction-shared.service';
import { Router, ActivatedRoute } from '@angular/router';
import { AccountService } from 'src/app/services/account.service';

fdescribe('DataIngestionFetchFileComponent', () => {
  let component: DataIngestionFetchFileComponent;
  let fixture: ComponentFixture<DataIngestionFetchFileComponent>;
  let fileSharingServiceMock: jasmine.SpyObj<FileSharingService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let dataIngestionServiceMock: jasmine.SpyObj<DataIngestionService>;
  let portfolioCompanyServiceMock: jasmine.SpyObj<PortfolioCompanyService>;
  let extractionSharedServiceMock: jasmine.SpyObj<ExtractionSharedService>;
  let routerMock: jasmine.SpyObj<Router>;

  const mockFileData = {
    selectedFilesList: [],
    selectedFiles: [],
    ingestionFormData: {
      company: {
        dealId: 1,
        companyId: 1,
        fundId: 1,
        companyName: 'Test Company',
        fundName: 'Test Fund'
      },
      module: 'test',
      monthQuarter: 'Q1',
      period: 'Quarter',
      sourceType: 'PDF',
      year: 2023
    }
  };

  const mockDocumentTypes = [
    { documentName: 'Type 1', id: 1 },
    { documentName: 'Type 2', id: 2 }
  ];
const mockPortfolioCompany = {
  imagePath: null
}
  beforeEach(async () => {
    fileSharingServiceMock = jasmine.createSpyObj('FileSharingService', [''], {
      currentFiles: new BehaviorSubject(mockFileData)
    });
    
    toastrServiceMock = jasmine.createSpyObj('ToastrService', 
      ['success', 'error', 'warning']);
    
    dataIngestionServiceMock = jasmine.createSpyObj('DataIngestionService', 
      ['getDataExtractionTypes']);
    dataIngestionServiceMock.getDataExtractionTypes.and.returnValue(of(mockDocumentTypes));

    portfolioCompanyServiceMock = jasmine.createSpyObj('PortfolioCompanyService', 
      ['getPortfolioCompanyLogo']);
    portfolioCompanyServiceMock.getPortfolioCompanyLogo.and.returnValue(of(mockPortfolioCompany));
    
    // Add mock for ExtractionSharedService
    extractionSharedServiceMock = jasmine.createSpyObj('ExtractionSharedService', 
      ['post']);
    extractionSharedServiceMock.post.and.returnValue(of([]));
    
    // Add mock for Router
    routerMock = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [ 
        DataIngestionFetchFileComponent
      ],
      imports: [ 
        ReactiveFormsModule,
        ToastrModule.forRoot(),
        KendoModule,
        HttpClientTestingModule
      ],
      providers: [
        FormBuilder,
        { provide: FileSharingService, useValue: fileSharingServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: DataIngestionService, useValue: dataIngestionServiceMock },
        { provide: PortfolioCompanyService, useValue: portfolioCompanyServiceMock },
        { provide: ExtractionSharedService, useValue: extractionSharedServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ActivatedRoute, useValue: { snapshot: {}, params: of({}), queryParams: of({}) } },
        { provide: AccountService, useValue: {} }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DataIngestionFetchFileComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct company data', () => {
    expect(component.companyName).toBe('Test Company');
    expect(component.companyInitials).toBe('TC');
    expect(component.reportingPeriod).toBe('Q1, 2023');
  });

  it('should load document types on init', () => {
    expect(dataIngestionServiceMock.getDataExtractionTypes).toHaveBeenCalled();
    expect(component.documentTypes).toEqual(mockDocumentTypes);
  });

  it('should handle file selection', () => {
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockEvent = {
      target: {
        files: [mockFile]
      }
    };

    component.onFileSelected(mockEvent);
    expect(component.selectedFilesList.length).toBeGreaterThan(0);
  });

  it('should validate max file count', () => {
    const mockFiles = Array(26).fill(new File([''], 'test.pdf', { type: 'application/pdf' }));
    const mockEvent = {
      target: {
        files: mockFiles
      }
    };

    component.onFileSelected(mockEvent);
    expect(toastrServiceMock.warning).toHaveBeenCalled();
  });

  it('should sort files correctly', () => {
    const mockFiles:SelectedFile[] = [
      { id: '1', name: 'B.pdf', status: 'invalid', errors: [], documentType: null, file: null, size: 0, type: '' },
      { id: '2', name: 'A.pdf', status: 'valid', errors: [], documentType: null, file: null, size: 0, type: '' }
    ];
    component.selectedFilesList = [...mockFiles];
    
    component.sortFilesByStatusAndName();
    
    expect(component.selectedFilesList[0].name).toBe('B.pdf');
    expect(component.selectedFilesList[0].status).toBe('invalid');
  });

  it('should handle document type change', () => {
    const mockFile:SelectedFile = {
      id: '1', name: 'test.pdf', status: 'valid', 
      errors: [], documentType: null, file: null, size: 0, type: ''
    };
    component.selectedFilesList = [mockFile];
    component.updateFormArray();

    component.onDocumentTypeChange('Type 1', 0);
    
    expect(component.selectedFilesList[0].documentType).toBe('Type 1');
    expect(component.formArray.at(0).get('documentType').value).toBe('Type 1');
  });

  it('should delete file correctly', () => {
    const mockFile:SelectedFile = {
      id: '1', name: 'test.pdf', status: 'valid', 
      errors: [], documentType: null, file: null, size: 0, type: ''
    };
    component.selectedFilesList = [mockFile];
    component.selectedFiles = [{ id: '1', name: 'test.pdf' }];
    component.updateFormArray();

    component.deleteFile(0, '1');
    
    expect(component.selectedFilesList.length).toBe(0);
    expect(component.selectedFiles.length).toBe(0);
  });

  it('should fetch portfolio company data when companyEncryptedID is available', () => {
    component.companyEncryptedID = 'test-id';
    component.getPortfolioCompanies();
    expect(portfolioCompanyServiceMock.getPortfolioCompanyLogo).toHaveBeenCalledWith({ Value: 'test-id' });
  });

  it('should not fetch portfolio company data when companyEncryptedID is null', () => {
    component.companyEncryptedID = null;
    component.getPortfolioCompanies();
    expect(portfolioCompanyServiceMock.getPortfolioCompanyLogo).not.toHaveBeenCalled();
  });
});
